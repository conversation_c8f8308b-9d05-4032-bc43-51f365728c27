using System.Text.Json;
using System.Text.Json.Serialization.Metadata;

namespace Sanet.MakaMek.Core.Data.Serialization;

/// <summary>
/// Composite type resolver that combines multiple type resolvers
/// </summary>
public class CompositeTypeResolver : DefaultJsonTypeInfoResolver
{
    private readonly IJsonTypeInfoResolver[] _resolvers;

    public CompositeTypeResolver(params IJsonTypeInfoResolver[] resolvers)
    {
        _resolvers = resolvers;
    }

    public override JsonTypeInfo GetTypeInfo(Type type, JsonSerializerOptions options)
    {
        // Try each resolver in order until one handles the type
        var defaultTypeInfo = base.GetTypeInfo(type, options);

        foreach (var resolver in _resolvers)
        {
            var typeInfo = resolver.GetTypeInfo(type, options);

            // If this resolver modified the type info (not just the default), use it
            // Compare by checking if polymorphism options were added or other modifications
            if (HasCustomizations(typeInfo, defaultTypeInfo))
            {
                return typeInfo;
            }
        }

        // Fall back to default behavior
        return defaultTypeInfo;
    }

    private static bool HasCustomizations(JsonTypeInfo typeInfo, JsonTypeInfo defaultTypeInfo)
    {
        // Check if polymorphism options were added
        if (typeInfo.PolymorphismOptions != null && defaultTypeInfo.PolymorphismOptions == null)
            return true;

        // Check if properties were modified
        if (typeInfo.Properties.Count != defaultTypeInfo.Properties.Count)
            return true;

        // Add other customization checks as needed
        return false;
    }
}
