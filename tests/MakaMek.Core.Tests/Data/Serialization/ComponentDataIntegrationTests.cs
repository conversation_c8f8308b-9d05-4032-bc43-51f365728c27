using System.Text.Json;
using Sanet.MakaMek.Core.Data.Serialization;
using Sanet.MakaMek.Core.Data.Units;
using Sanet.MakaMek.Core.Data.Units.Components;
using Sanet.MakaMek.Core.Models.Units;
using Sanet.MakaMek.Core.Models.Units.Components.Engines;
using Shouldly;

namespace Sanet.MakaMek.Core.Tests.Data.Serialization;

public class ComponentDataIntegrationTests
{
    private static readonly JsonSerializerOptions JsonOptions = new()
    {
        TypeInfoResolver = new CompositeTypeResolver(
            new RollModifierTypeResolver(),
            new ComponentSpecificDataTypeResolver()),
        WriteIndented = true
    };

    [Fact]
    public void UnitData_WithComponentsHavingSpecificData_SerializesAndDeserializesCorrectly()
    {
        // Arrange
        var originalUnitData = new UnitData
        {
            Id = Guid.NewGuid(),
            Chassis = "Atlas",
            Model = "AS7-D",
            Mass = 100,
            WalkMp = 3,
            EngineRating = 300,
            EngineType = "Fusion",
            ArmorValues = new Dictionary<PartLocation, ArmorLocation>
            {
                { PartLocation.Head, new ArmorLocation { FrontArmor = 9 } },
                { PartLocation.CenterTorso, new ArmorLocation { FrontArmor = 47, RearArmor = 14 } }
            },
            Equipment = new List<ComponentData>
            {
                // Engine with EngineStateData
                new ComponentData
                {
                    Type = MakaMekComponent.Engine,
                    Assignments = new List<LocationSlotAssignment>
                    {
                        new(PartLocation.CenterTorso, 0, 3),
                        new(PartLocation.CenterTorso, 7, 3)
                    },
                    Hits = 0,
                    IsActive = true,
                    HasExploded = false,
                    SpecificData = new EngineStateData(EngineType.Fusion, 300)
                },
                // Ammo with AmmoStateData
                new ComponentData
                {
                    Type = MakaMekComponent.ISAmmoAC20,
                    Assignments = new List<LocationSlotAssignment>
                    {
                        new(PartLocation.LeftTorso, 0, 1)
                    },
                    Hits = 0,
                    IsActive = true,
                    HasExploded = false,
                    SpecificData = new AmmoStateData(5)
                },
                // Regular component without SpecificData
                new ComponentData
                {
                    Type = MakaMekComponent.MediumLaser,
                    Assignments = new List<LocationSlotAssignment>
                    {
                        new(PartLocation.RightArm, 0, 1)
                    },
                    Hits = 0,
                    IsActive = true,
                    HasExploded = false,
                    SpecificData = null
                }
            },
            AdditionalAttributes = new Dictionary<string, string>(),
            Quirks = new Dictionary<string, string>()
        };

        // Act
        var json = JsonSerializer.Serialize(originalUnitData, JsonOptions);
        var deserializedUnitData = JsonSerializer.Deserialize<UnitData>(json, JsonOptions);

        // Assert
        deserializedUnitData.Id.ShouldBe(originalUnitData.Id);
        deserializedUnitData.Chassis.ShouldBe("Atlas");
        deserializedUnitData.Model.ShouldBe("AS7-D");
        deserializedUnitData.Equipment.Count.ShouldBe(3);

        // Check Engine component
        var engineComponent = deserializedUnitData.Equipment.First(e => e.Type == MakaMekComponent.Engine);
        engineComponent.SpecificData.ShouldNotBeNull();
        engineComponent.SpecificData.ShouldBeOfType<EngineStateData>();
        var engineData = (EngineStateData)engineComponent.SpecificData;
        engineData.Type.ShouldBe(EngineType.Fusion);
        engineData.Rating.ShouldBe(300);

        // Check Ammo component
        var ammoComponent = deserializedUnitData.Equipment.First(e => e.Type == MakaMekComponent.ISAmmoAC20);
        ammoComponent.SpecificData.ShouldNotBeNull();
        ammoComponent.SpecificData.ShouldBeOfType<AmmoStateData>();
        var ammoData = (AmmoStateData)ammoComponent.SpecificData;
        ammoData.RemainingShots.ShouldBe(5);

        // Check regular component
        var laserComponent = deserializedUnitData.Equipment.First(e => e.Type == MakaMekComponent.MediumLaser);
        laserComponent.SpecificData.ShouldBeNull();
    }

    [Fact]
    public void ComponentData_WithEngineStateData_SerializesAndDeserializesCorrectly()
    {
        // Arrange
        var originalData = new ComponentData
        {
            Type = MakaMekComponent.Engine,
            Assignments = new List<LocationSlotAssignment>
            {
                new(PartLocation.CenterTorso, 0, 3),
                new(PartLocation.CenterTorso, 7, 3)
            },
            Hits = 1,
            IsActive = true,
            HasExploded = false,
            Name = "Test Engine",
            Manufacturer = "Test Manufacturer",
            SpecificData = new EngineStateData(EngineType.XLFusion, 250)
        };

        // Act
        var json = JsonSerializer.Serialize(originalData, JsonOptions);
        var deserializedData = JsonSerializer.Deserialize<ComponentData>(json, JsonOptions);

        // Assert
        deserializedData.ShouldNotBeNull();
        deserializedData.Type.ShouldBe(MakaMekComponent.Engine);
        deserializedData.Assignments.Count.ShouldBe(2);
        deserializedData.Hits.ShouldBe(1);
        deserializedData.IsActive.ShouldBeTrue();
        deserializedData.HasExploded.ShouldBeFalse();
        deserializedData.Name.ShouldBe("Test Engine");
        deserializedData.Manufacturer.ShouldBe("Test Manufacturer");
        
        deserializedData.SpecificData.ShouldNotBeNull();
        deserializedData.SpecificData.ShouldBeOfType<EngineStateData>();
        
        var engineData = (EngineStateData)deserializedData.SpecificData;
        engineData.Type.ShouldBe(EngineType.XLFusion);
        engineData.Rating.ShouldBe(250);
    }

    [Fact]
    public void ComponentData_WithAmmoStateData_SerializesAndDeserializesCorrectly()
    {
        // Arrange
        var originalData = new ComponentData
        {
            Type = MakaMekComponent.ISAmmoLRM20,
            Assignments = new List<LocationSlotAssignment>
            {
                new(PartLocation.RightTorso, 2, 1)
            },
            Hits = 0,
            IsActive = true,
            HasExploded = false,
            SpecificData = new AmmoStateData(12)
        };

        // Act
        var json = JsonSerializer.Serialize(originalData, JsonOptions);
        var deserializedData = JsonSerializer.Deserialize<ComponentData>(json, JsonOptions);

        // Assert
        deserializedData.ShouldNotBeNull();
        deserializedData.Type.ShouldBe(MakaMekComponent.ISAmmoLRM20);
        deserializedData.Assignments.Count.ShouldBe(1);
        deserializedData.Hits.ShouldBe(0);
        deserializedData.IsActive.ShouldBeTrue();
        deserializedData.HasExploded.ShouldBeFalse();
        
        deserializedData.SpecificData.ShouldNotBeNull();
        deserializedData.SpecificData.ShouldBeOfType<AmmoStateData>();
        
        var ammoData = (AmmoStateData)deserializedData.SpecificData;
        ammoData.RemainingShots.ShouldBe(12);
    }
}
