using System.Text.Json;
using Sanet.MakaMek.Core.Data.Game.Commands.Client;
using Sanet.MakaMek.Core.Data.Serialization;
using Sanet.MakaMek.Core.Data.Units;
using Sanet.MakaMek.Core.Data.Units.Components;
using Sanet.MakaMek.Core.Models.Units;
using Sanet.MakaMek.Core.Models.Units.Components.Engines;
using Sanet.MakaMek.Core.Services.Transport;
using Shouldly;

namespace Sanet.MakaMek.Core.Tests.Services.Transport;

public class CommandTransportAdapterSerializationTests
{
    [Fact]
    public void CommandTransportAdapter_SerializesAndDeserializesJoinGameCommandWithComponentData()
    {
        // Arrange
        var adapter = new CommandTransportAdapter();
        
        var unitData = new UnitData
        {
            Id = Guid.NewGuid(),
            Chassis = "Locust",
            Model = "LCT-1V",
            Mass = 20,
            WalkMp = 8,
            EngineRating = 160,
            EngineType = "Fusion",
            ArmorValues = new Dictionary<PartLocation, ArmorLocation>
            {
                { PartLocation.Head, new ArmorLocation { FrontArmor = 3 } },
                { PartLocation.CenterTorso, new ArmorLocation { FrontArmor = 5, RearArmor = 3 } }
            },
            Equipment = new List<ComponentData>
            {
                // Engine with EngineStateData
                new ComponentData
                {
                    Type = MakaMekComponent.Engine,
                    Assignments = new List<LocationSlotAssignment>
                    {
                        new(PartLocation.CenterTorso, 0, 3),
                        new(PartLocation.CenterTorso, 7, 3)
                    },
                    Hits = 0,
                    IsActive = true,
                    HasExploded = false,
                    SpecificData = new EngineStateData(EngineType.Fusion, 160)
                },
                // Ammo with AmmoStateData
                new ComponentData
                {
                    Type = MakaMekComponent.ISAmmoMG,
                    Assignments = new List<LocationSlotAssignment>
                    {
                        new(PartLocation.LeftTorso, 0, 1)
                    },
                    Hits = 0,
                    IsActive = true,
                    HasExploded = false,
                    SpecificData = new AmmoStateData(200)
                }
            },
            AdditionalAttributes = new Dictionary<string, string>(),
            Quirks = new Dictionary<string, string>()
        };

        var pilotData = PilotData.CreateDefaultPilot("Test", "Pilot");
        var pilotAssignment = new PilotAssignmentData
        {
            UnitId = unitData.Id!.Value,
            PilotData = pilotData
        };

        var originalCommand = new JoinGameCommand
        {
            GameOriginId = Guid.NewGuid(),
            Timestamp = DateTime.UtcNow,
            PlayerId = Guid.NewGuid(),
            PlayerName = "Test Player",
            Units = [unitData],
            PilotAssignments = [pilotAssignment],
            Tint = "#FF0000"
        };

        // Act - Serialize the command
        var serializedJson = JsonSerializer.Serialize(originalCommand, originalCommand.GetType(),
            new JsonSerializerOptions
            {
                TypeInfoResolver = new CompositeTypeResolver(
                    new RollModifierTypeResolver(),
                    new ComponentSpecificDataTypeResolver()),
                WriteIndented = true
            });

        // Deserialize the command
        var deserializedCommand = JsonSerializer.Deserialize<JoinGameCommand>(serializedJson,
            new JsonSerializerOptions
            {
                TypeInfoResolver = new CompositeTypeResolver(
                    new RollModifierTypeResolver(),
                    new ComponentSpecificDataTypeResolver()),
                WriteIndented = true
            });

        // Assert
        deserializedCommand.PlayerName.ShouldBe("Test Player");
        deserializedCommand.Units.Count.ShouldBe(1);

        var deserializedUnitData = deserializedCommand.Units.First();
        deserializedUnitData.Chassis.ShouldBe("Locust");
        deserializedUnitData.Equipment.Count.ShouldBe(2);

        // Check Engine component
        var engineComponent = deserializedUnitData.Equipment.First(e => e.Type == MakaMekComponent.Engine);
        engineComponent.SpecificData.ShouldNotBeNull();
        engineComponent.SpecificData.ShouldBeOfType<EngineStateData>();
        var engineData = (EngineStateData)engineComponent.SpecificData;
        engineData.Type.ShouldBe(EngineType.Fusion);
        engineData.Rating.ShouldBe(160);

        // Check Ammo component
        var ammoComponent = deserializedUnitData.Equipment.First(e => e.Type == MakaMekComponent.ISAmmoMG);
        ammoComponent.SpecificData.ShouldNotBeNull();
        ammoComponent.SpecificData.ShouldBeOfType<AmmoStateData>();
        var ammoData = (AmmoStateData)ammoComponent.SpecificData;
        ammoData.RemainingShots.ShouldBe(200);
    }
}
